// Import required modules
require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const sgMail = require('@sendgrid/mail');
const OpenAI = require('openai');
const admin = require('firebase-admin');
const cors = require('cors');
const axios = require('axios');
const NodeCache = require('node-cache');
const roleCache = new NodeCache({ stdTTL: 3600 });
const requestCache = new NodeCache({ stdTTL: 3600 }); // Cache for 1 hour

// Active requests tracking to prevent race conditions
const activeRequests = new Set();

// Helper function to check for duplicate requests within a session
function isDuplicateRequest(email, endpoint, params) {
  if (!email) return false;

  const cacheKey = `${email}_${endpoint}_${JSON.stringify(params)}`;

  // Check both the cache and active requests
  if (requestCache.has(cacheKey) || activeRequests.has(cacheKey)) {
    console.log(`Duplicate request detected: ${cacheKey}`);
    return true;
  }

  // Mark as active immediately to prevent race conditions
  activeRequests.add(cacheKey);
  return false;
}

// Helper function to track processed requests
function trackRequest(email, endpoint, params) {
  if (!email) return;

  const cacheKey = `${email}_${endpoint}_${JSON.stringify(params)}`;
  requestCache.set(cacheKey, true);

  // Remove from active requests
  activeRequests.delete(cacheKey);

  console.log(`Request tracked: ${cacheKey}`);
}

// Helper function to release a request if it fails
function releaseRequest(email, endpoint, params) {
  if (!email) return;

  const cacheKey = `${email}_${endpoint}_${JSON.stringify(params)}`;
  activeRequests.delete(cacheKey);
  console.log(`Request released: ${cacheKey}`);
}

// Function to extract courses from learning path JSON
function extractLearningPathData() {
  try {
    // Require the learning path data file
    const learningPathData = require('./learning-path-data.json');

    // Initialize an object to store all course information
    const courseData = {
      pathways: [],
      allCourses: [],
      coursesByPathway: {},
      coursesByCategory: {}
    };

    // Process each learning pathway
    for (const pathway of ['essentials', 'intermediate', 'advanced', 'champions']) {
      if (!learningPathData[pathway]) continue;

      const pathwayTitle = learningPathData[pathway].title;
      courseData.pathways.push(pathwayTitle);
      courseData.coursesByPathway[pathwayTitle] = [];

      // Process each category in the pathway
      learningPathData[pathway].courseCategories.forEach(category => {
        const categoryName = category.category;

        if (!courseData.coursesByCategory[categoryName]) {
          courseData.coursesByCategory[categoryName] = [];
        }

        // Process each course in the category
        category.courses.forEach(course => {
          const courseInfo = {
            title: course.title,
            description: course.description,
            pathway: pathwayTitle,
            category: categoryName,
            level: course.level
          };

          // Add to all relevant collections
          courseData.allCourses.push(courseInfo);
          courseData.coursesByPathway[pathwayTitle].push(courseInfo);
          courseData.coursesByCategory[categoryName].push(courseInfo);
        });
      });
    }

    // Calculate progression chains
    courseData.progressionChains = calculateProgressionChains(courseData.allCourses);

    return courseData;
  } catch (error) {
    console.error('Error extracting learning path data:', error);
    return null;
  }
}

// Helper function to identify course progression chains
function calculateProgressionChains(allCourses) {
  const chains = {};

  // Group courses by base name (without level)
  const coursesByBase = {};
  allCourses.forEach(course => {
    const baseName = course.title.split(' - ')[0];
    if (!coursesByBase[baseName]) {
      coursesByBase[baseName] = [];
    }
    coursesByBase[baseName].push(course);
  });

  // For each base course type, create progression chains
  Object.keys(coursesByBase).forEach(baseName => {
    const courses = coursesByBase[baseName];

    // Sort by pathway level (Essentials -> Intermediate -> Advanced -> Champions)
    const sortedCourses = courses.sort((a, b) => {
      const pathwayOrder = {
        'Essentials Learning Pathway': 1,
        'Intermediate Learning Pathway': 2,
        'Advanced Learning Pathway': 3,
        'Champions Learning Pathway': 4
      };
      return pathwayOrder[a.pathway] - pathwayOrder[b.pathway];
    });

    // Create chains where each course points to courses at the next level
    sortedCourses.forEach((course, index) => {
      if (index < sortedCourses.length - 1) {
        chains[course.title] = sortedCourses.slice(index + 1).map(c => c.title);
      } else {
        chains[course.title] = [];
      }
    });
  });

  return chains;
}

// Initialize Express app
const app = express();
const port = process.env.PORT || 3000;

// Global variable to store course data
let globalLearningPathData;

// Initialize learning path data at startup
async function initializeLearningPathData() {
  console.log('Initializing learning path data...');
  globalLearningPathData = extractLearningPathData();

  if (globalLearningPathData) {
    console.log('Learning path data loaded successfully');
    console.log(`Loaded ${globalLearningPathData.allCourses.length} courses across ${globalLearningPathData.pathways.length} pathways`);
  } else {
    console.error('Failed to load learning path data');
  }
}

// Configure middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Import your Firebase service account key JSON file
const serviceAccount = require('./service_account.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/"
});

// Initialize Firebase services
const db = admin.database();
const firestore = admin.firestore();
const processedResultsRef = db.ref('processedResults');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Function to send email using SendGrid
const sendMail = async (to, templateId, dynamicData) => {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  const msg = {
    to,
    from: {
      name: 'Barefoot eLearning',
      email: process.env.FROM_EMAIL
    },
    templateId,
    dynamicTemplateData: dynamicData,
    isMultiple: true,
    use_template_subject: true
  };
  try {
    await sgMail.send(msg);
    console.log('Email sent');
  } catch (error) {
    console.error(error);
    if (error.response) {
      console.error(error.response.body)
    }
  }
}

// Route handler for the root path
app.get('/', (_, res) => {
  res.send('Hello, World!');
});

// Route to receive the key from the client
app.post('/receive-key', async (req, res) => {
  const key = req.body.key;
  console.log('Received key from client:', key);

  if (!key) {
    console.error('No key provided in the request body');
    return res.status(400).json({ error: 'Invalid request: No key provided' });
  }

  try {
    const userRef = db.ref('users').child(key);
    const userSnapshot = await userRef.once('value');
    const userData = userSnapshot.val();

    if (userData) {
      const { firstName, 'lite assesment score': score, userEmail } = userData;
      const totalQuestions = 10;
      const scorePercentage = (score / totalQuestions) * 100;

      let remarks;
      if (scorePercentage < 30) {
        remarks = "Low knowledge";
      } else if (scorePercentage < 50) {
        remarks = "Fair knowledge";
      } else if (scorePercentage >= 50 && scorePercentage < 70) {
        remarks = "Good knowledge ";
      } else {
        remarks = "Excellent knowledge";
      }

      const dynamicData = {
        name: firstName,
        score: `${scorePercentage.toFixed(0)}%`,
        key,
        remarks
      };

      await sendMail(userEmail, process.env.TEMPLATE_ID_LITE, dynamicData);
      res.status(200).json({ message: 'Email sent successfully' });
    } else {
      console.error(`User not found for key: ${key}`);
      res.status(404).json({ error: 'User not found' });
    }
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

async function fetchOrGenerateFramework(role) {
  try {
    // Try to retrieve from Firestore
    const frameworkDoc = await firestore.collection('frameworks').doc(role).get();
    if (frameworkDoc.exists) {
      console.log(`Framework retrieved from cache for role: ${role}`);
      return frameworkDoc.data();
    }

    console.log(`No cached framework found for role: ${role}. Generating a new one...`);

    // If not found in Firestore, call the generate-framework endpoint
    const response = await axios.post(`http://localhost:${port}/api/generate-framework`, { role });
    return response.data;
  } catch (error) {
    console.error('Error fetching or generating framework:', error);
    return null;
  }
}

async function getRecommendationsFromOpenAIAssistant(role, learningPath, learningPathData, userResponses) {
  try {
    console.log('Analyzing skills with inputs:', {
      role,
      learningPath,
      hasLearningPathData: !!learningPathData,
      responseCount: userResponses?.length
    });

    // Validate required inputs
    if (!role || !learningPath || !learningPathData || !userResponses) {
      throw new Error('Missing required parameters for recommendation generation');
    }

    // Ensure we have the global learning path data loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded for recommendations, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        throw new Error('Learning path data could not be loaded');
      }
    }

    // Separate knowledge check and self-assessment responses
    let knowledgeCheckResponses = [];
    let selfAssessmentResponses = [];

    if (userResponses.responseTypes) {
      knowledgeCheckResponses = userResponses.responseTypes.knowledgeCheck || [];
      selfAssessmentResponses = userResponses.responseTypes.selfAssessment || [];
    } else {
      knowledgeCheckResponses = userResponses.filter(r => r.questionType === 'knowledge-check' || !r.questionType);
      selfAssessmentResponses = userResponses.filter(r => r.questionType === 'self-assessment');
    }

    // Construct the initial analysis context with just user data and role
    const initialAnalysisContext = {
      role,
      assessmentData: {
        knowledgeCheck: {
          responses: knowledgeCheckResponses,
          summary: {
            totalQuestions: knowledgeCheckResponses.length,
            correctAnswers: knowledgeCheckResponses.filter(r => r.isCorrect).length
          }
        },
        selfAssessment: {
          responses: selfAssessmentResponses,
          summary: {
            totalQuestions: selfAssessmentResponses.length,
            skillLevels: categorizeSkillLevels(selfAssessmentResponses)
          }
        }
      }
    };

    // STEP 1: Get competency analysis based on user responses
    const initialInstructions = `
    You are analyzing performance for a ${role} based on assessment responses.

    Your task is to generate a detailed competency analysis with multiple skill areas that are relevant to this role.

    For each skill area:
    1. Determine a proficiency level as a percentage
    2. Identify strength areas
    3. Identify gap areas in the format "SkillArea - Specific Topic" (e.g., "Excel - creating advanced formulas")
    4. Suggest a progression path

    Also provide an overall summary of the user's skills.

    CRITICAL NOTE ABOUT SELF-ASSESSMENT:
    - The assessment includes both knowledge check questions (with correct/incorrect answers) and self-assessment questions (without correct answers).
    - For self-assessment responses, consider the selected option as an indicator of the user's skill level:
      * Option 1 typically indicates beginner/basic level
      * Option 2 typically indicates intermediate level
      * Option 3 typically indicates advanced level
    - Incorporate self-assessment responses when determining competency gaps - if a user selects basic options for a skill area, this indicates a potential training need.

    IMPORTANT: Output Must only be in JSON format. Do not include any other content/explanation or formatting.
    `;

    // Define the function schema for competency analysis - make schema more flexible for GPT-4.1-mini
    const generate_competency_analysis_function = {
      name: "generate_competency_analysis",
      description: "Generate a competency analysis with multiple skill areas, ensuring each gap is 'SkillArea - Specific Topic'.",
      parameters: {
        type: "object",
        properties: {
          competencyAnalysis: {
            type: "object",
            description: "Object containing skill areas as keys, each with proficiency, strengths, gaps, and progression",
            additionalProperties: {
              type: "object",
              properties: {
                proficiencyLevel: { type: "string" },
                strengthAreas: { type: "array", items: { type: "string" } },
                gapAreas: { type: "array", items: { type: "string" } },
                progressionPath: { type: "array", items: { type: "string" } }
              },
              required: ["proficiencyLevel", "strengthAreas", "gapAreas", "progressionPath"]
            }
          },
          summary: { type: "string", description: "Overall summary of the user's skills and development needs" }
        },
        required: ["competencyAnalysis", "summary"]
      }
    };

    // Alternative simpler schema as backup
    const simple_competency_analysis_function = {
      name: "generate_competency_analysis",
      description: "Generate a competency analysis with multiple skill areas",
      parameters: {
        type: "object",
        properties: {
          analysis: {
            type: "object",
            description: "The competency analysis with skill areas"
          },
          summary: {
            type: "string",
            description: "Overall summary of skills"
          }
        },
        required: ["analysis", "summary"]
      }
    };

    // Calculate tokens for initial API call
    const { encode } = require('gpt-tokenizer');
    const initialMessages = [
      { role: "system", content: initialInstructions },
      { role: "user", content: JSON.stringify(initialAnalysisContext) }
    ];

    const initialPromptTokens = initialMessages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 128000; // GPT-4.1 Mini's context size (estimated)
    const maxResponseTokens = 8000; // GPT-4.1 Mini's max output (estimated)
    const initialAvailableTokens = contextWindow - initialPromptTokens;
    const initial_max_tokens = Math.min(maxResponseTokens, initialAvailableTokens);

    if (initial_max_tokens <= 0) {
      throw new Error(`Initial prompt exceeds context window (${initialPromptTokens}/${contextWindow} tokens)`);
    }

    console.log('Sending competency analysis request to GPT-4.1-mini...');

    // Call GPT-4.1 Mini for competency analysis with extensive debugging
    console.log('Sending competency analysis request to GPT-4.1-mini with detailed instructions...');

    // Try a simple direct approach first - no function calling, just a direct prompt
    try {
      const directPrompt = `
You are analyzing a ${role}'s performance based on assessment responses.

Based on the assessment data provided below, create a detailed competency analysis with at least 3 skill areas.

For each skill area include:
1. A proficiency level (as a percentage)
2. 2-4 strength areas
3. 2-4 gap areas (formatted as "SkillArea - Specific Topic")
4. A progression path

Format your response as valid JSON with this exact structure:
{
  "competencyAnalysis": {
    "Skill Area 1": {
      "proficiencyLevel": "X%",
      "strengthAreas": ["Strength 1", "Strength 2"],
      "gapAreas": ["Area - Gap 1", "Area - Gap 2"],
      "progressionPath": ["Step 1", "Step 2"]
    },
    "Skill Area 2": {
      "proficiencyLevel": "Y%",
      "strengthAreas": ["Strength 1", "Strength 2"],
      "gapAreas": ["Area - Gap 1", "Area - Gap 2"],
      "progressionPath": ["Step 1", "Step 2"]
    },
    "Skill Area 3": {
      "proficiencyLevel": "Z%",
      "strengthAreas": ["Strength 1", "Strength 2"],
      "gapAreas": ["Area - Gap 1", "Area - Gap 2"],
      "progressionPath": ["Step 1", "Step 2"]
    }
  },
  "summary": "Overall summary of performance..."
}

Here is the assessment data:
${JSON.stringify(initialAnalysisContext)}

ONLY respond with the JSON. Do not include any other text.
      `;

      console.log('Trying direct approach without function calling');

      const directCompletion = await openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          { role: "system", content: directPrompt },
        ],
        max_tokens: initial_max_tokens
      });

      console.log('Direct completion response received');
      const directContent = directCompletion.choices[0].message.content;
      console.log('Raw response content:', directContent);

      // Try to parse the direct response as JSON
      try {
        // Find JSON in the response - look for everything between first { and last }
        const jsonMatch = directContent.match(/\{[\s\S]*\}/);
        const jsonString = jsonMatch ? jsonMatch[0] : directContent;

        console.log('Attempting to parse JSON:', jsonString);
        competencyAnalysis = JSON.parse(jsonString);

        console.log('Successfully parsed direct JSON response');

        // Validate the direct response structure
        if (!competencyAnalysis.competencyAnalysis) {
          console.warn('Direct response missing competencyAnalysis field, trying function calling approach');
          throw new Error('Invalid direct response structure');
        }

        console.log('Direct approach successful, skipping function-calling approach');

      } catch (err) {
        console.warn(`Failed to parse direct response: ${err.message}, trying function calling approach`);
        throw err; // Proceed to function calling approach
      }

    } catch (err) {
      // If direct approach fails, try function calling
      console.log('Falling back to function calling approach');

      const competencyCompletion = await openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          {
            role: "system",
            content: `${initialInstructions}\n\nIMPORTANT: Your response MUST include a complete JSON object with both a "competencyAnalysis" object and a "summary" string. Failure to include BOTH of these fields is unacceptable.`
          },
          { role: "user", content: JSON.stringify(initialAnalysisContext) }
        ],
        tools: [{
          type: "function",
          function: simple_competency_analysis_function  // Use the simpler schema
        }],
        tool_choice: { type: "function", function: { name: "generate_competency_analysis" } },
        max_tokens: initial_max_tokens
      });

      console.log('Function calling response received:', JSON.stringify(competencyCompletion, null, 2));

      const competencyMessage = competencyCompletion.choices[0].message;

      // Detailed logging to debug the API response
      if (!competencyMessage.tool_calls || competencyMessage.tool_calls.length === 0) {
        console.error('ERROR: No tool calls found in API response:', JSON.stringify(competencyMessage, null, 2));

        // As a fallback, see if there's content directly in the message
        if (competencyMessage.content) {
          console.log('Found content directly in message, trying to parse:');
          try {
            // Try to extract JSON from the content
            const jsonMatch = competencyMessage.content.match(/\{[\s\S]*\}/);
            const jsonString = jsonMatch ? jsonMatch[0] : competencyMessage.content;

            competencyAnalysis = JSON.parse(jsonString);
            console.log('Successfully parsed JSON from message content');

            // If the JSON doesn't have competencyAnalysis but has analysis, rename it
            if (!competencyAnalysis.competencyAnalysis && competencyAnalysis.analysis) {
              competencyAnalysis.competencyAnalysis = competencyAnalysis.analysis;
              delete competencyAnalysis.analysis;
              console.log('Renamed "analysis" field to "competencyAnalysis"');
            }

          } catch (parseErr) {
            console.error('Failed to parse content as JSON:', parseErr.message);
            throw new Error("Could not generate valid competency analysis");
          }
        } else {
          throw new Error("API response did not include any tool calls or content");
        }
      } else {
        // Process tool calls
        if (competencyMessage.tool_calls[0].function.name !== "generate_competency_analysis") {
          console.error('ERROR: Wrong function called:', competencyMessage.tool_calls[0].function.name);
          throw new Error("API called the wrong function");
        }

        console.log('Function arguments from competency analysis:', competencyMessage.tool_calls[0].function.arguments);

        try {
          competencyAnalysis = JSON.parse(competencyMessage.tool_calls[0].function.arguments);
          console.log('Parsed competency analysis from function arguments');

          // If the JSON doesn't have competencyAnalysis but has analysis, rename it
          if (!competencyAnalysis.competencyAnalysis && competencyAnalysis.analysis) {
            competencyAnalysis.competencyAnalysis = competencyAnalysis.analysis;
            delete competencyAnalysis.analysis;
            console.log('Renamed "analysis" field to "competencyAnalysis"');
          }

        } catch (parseErr) {
          console.error('Failed to parse function arguments:', parseErr.message);
          throw new Error("Could not parse competency analysis from function arguments");
        }
      }
    }

    // Final validation of competency analysis structure
    console.log('Final competency analysis structure:', JSON.stringify(competencyAnalysis, null, 2));

    if (!competencyAnalysis.competencyAnalysis || typeof competencyAnalysis.competencyAnalysis !== 'object') {
      console.error('ERROR: Missing competencyAnalysis object in parsed response:', JSON.stringify(competencyAnalysis, null, 2));

      // Create a fallback competency analysis as a last resort
      console.warn('CREATING FALLBACK COMPETENCY ANALYSIS');
      competencyAnalysis.competencyAnalysis = {
        "Digital Proficiency": {
          "proficiencyLevel": "65%",
          "strengthAreas": [
            "Basic software navigation",
            "Understanding of digital tools",
            "Familiarity with common applications"
          ],
          "gapAreas": [
            `${role} - Advanced features`,
            "Digital Collaboration - Team coordination"
          ],
          "progressionPath": [
            "Basics to intermediate tools",
            "Specialized role-specific applications"
          ]
        },
        "Productivity": {
          "proficiencyLevel": "70%",
          "strengthAreas": [
            "Task management",
            "Basic document creation"
          ],
          "gapAreas": [
            "Office Suite - Advanced functions",
            "Workflow Optimization - Process efficiency"
          ],
          "progressionPath": [
            "Essential office tools",
            "Advanced productivity techniques"
          ]
        },
        "Technical Knowledge": {
          "proficiencyLevel": "60%",
          "strengthAreas": [
            "Fundamental concepts",
            "Standard procedures"
          ],
          "gapAreas": [
            "Technical Skills - Role-specific requirements",
            "Systems Knowledge - Integration capabilities"
          ],
          "progressionPath": [
            "Foundational technical skills",
            "Specialized domain knowledge"
          ]
        }
      };
    }

    // Ensure summary exists with meaningful content
    if (!competencyAnalysis.summary) {
      console.warn('Missing summary field, creating one');
      competencyAnalysis.summary = `Based on the assessment results for a ${role}, several key skill areas require development. Focus should be on building core technical capabilities relevant to the role while enhancing productivity skills. The assessment indicates particular development needs in advanced features of commonly used tools.`;
    }

    // STEP 2: Generate pathway courses text for recommendations
    // Generate formatted course data for each pathway
    const pathwayCoursesText = [];
    globalLearningPathData.pathways.forEach(pathway => {
      // Extract the level number
      const levelMatch = pathway.match(/(\w+)\s+Learning\s+Pathway/i);
      let level = levelMatch ? levelMatch[1] : pathway;

      // Determine numeric sequence for this level
      let sequenceNumber;
      switch(level.toLowerCase()) {
        case 'essentials': sequenceNumber = "1"; break;
        case 'intermediate': sequenceNumber = "2"; break;
        case 'advanced': sequenceNumber = "3"; break;
        case 'champions': sequenceNumber = "4"; break;
        default: sequenceNumber = "";
      }

      pathwayCoursesText.push(`${sequenceNumber} ${level.toUpperCase()} LEARNING PATHWAY\n`);

      // Group courses by category within this pathway
      const categoriesInPathway = {};
      globalLearningPathData.coursesByPathway[pathway].forEach(course => {
        if (!categoriesInPathway[course.category]) {
          categoriesInPathway[course.category] = [];
        }
        categoriesInPathway[course.category].push(course.title);
      });

      // Add formatted category and course listings
      Object.keys(categoriesInPathway).forEach(category => {
        pathwayCoursesText.push(`${category}:`);
        categoriesInPathway[category].forEach(courseTitle => {
          pathwayCoursesText.push(`"${courseTitle}"`);
        });
        pathwayCoursesText.push('');
      });

      pathwayCoursesText.push('');
    });

    // Generate progression patterns text
    const progressionPatternsText = ['Key Complete Progression Patterns (with exact titles):'];

    // Get the most common progression chains
    const mainCourseTypes = [
      'Excel', 'Word', 'PowerPoint', 'Outlook', 'Teams', 'SharePoint', 'Cyber Security'
    ];

    mainCourseTypes.forEach(baseType => {
      const chains = [];
      globalLearningPathData.allCourses.forEach(course => {
        if (course.title.startsWith(baseType)) {
          chains.push(course.title);
        }
      });

      if (chains.length > 0) {
        // Sort by pathway
        chains.sort((a, b) => {
          const pathwayOrder = {
            'Essentials': 1,
            'Intermediate': 2,
            'Advanced': 3,
            'Champions': 4
          };

          const aLevel = a.split(' - ')[1];
          const bLevel = b.split(' - ')[1];
          return pathwayOrder[aLevel] - pathwayOrder[bLevel];
        });

        progressionPatternsText.push(`${baseType}:`);
        progressionPatternsText.push(`"${chains.join('" → "')}"`, '');
      }
    });

    // Create recommendation context with competency analysis and learning path data
    const recommendationContext = {
      role,
      currentPath: learningPath,
      competencyAnalysis: competencyAnalysis.competencyAnalysis,
      summary: competencyAnalysis.summary
    };

    console.log('Creating recommendation context with competency analysis:',
      JSON.stringify(recommendationContext, null, 2));

    // Instructions for course recommendations
    const recommendationInstructions = `
    You are generating course recommendations for a ${role} in the ${learningPath} learning path.

    IMPORTANT OVERVIEW:
    - If a user is currently at the "Essentials" path, recommend relevant courses from "Essentials" and then include higher-level courses ("Intermediate" → "Advanced" → "Champions") that build upon those skills.
    - If a user is currently at the "Intermediate" path, recommend only "Intermediate" courses and then potential progressions from "Advanced" → "Champions" (never below "Intermediate").
    - If a user is currently at the "Advanced" path, recommend only "Advanced" courses and then potential progressions from "Champions" (never below "Advanced").
    - If a user is at the "Champions" path, do not recommend any additional courses.
    - The progression should be complete all the way to "Champions" for any user level below "Champions."
      * For example, if recommending "Excel - Essentials" (at Essentials level), you must also recommend "Excel - Intermediate" (Intermediate) and "Excel - Advanced" (Advanced).
      * Similarly, if a user is at the "Intermediate" level for Excel, you must still include relevant Advanced-level (and Champions-level if applicable) courses from the learning-path-data.

    CRITICAL REQUIREMENTS FOR COURSE RECOMMENDATIONS:
    1. You MUST ONLY recommend courses that exist in the learning-path-data.
    2. Course names in recommendations MUST EXACTLY match the "title" field from the courses in the learning-path-data.
    3. Do not invent or suggest courses that don't exist in the learning path data.
    4. For cross-path recommendations:
       - When recommending a course in a specific area (e.g., Excel), you MUST include related progressive courses from other paths — but only at or above the user's current level.
       - For example: If recommending "Excel - Essentials" (Essentials), also suggest "Excel - Intermediate" (Intermediate) and "Excel - Advanced" (Advanced).
       - Ensure all recommendations form a clear learning progression path.
       - Only suggest courses from higher-level paths that build upon the base recommendation — not lower levels.

    ADDITIONAL CRITICAL NOTE #1:
    - If the competency analysis or summary identifies a specific gap by name (e.g., "Planner"), and it exists in the learning-path data, you MUST include it in "recommendations" **if**:
      (a) The user's current path level allows it (no recommending from a level below the user's current level).
      (b) It does not violate the progression rules (if the user is at Essentials, you can still recommend that advanced course by including the appropriate progression from Intermediate → Advanced, etc.).
    - Gaps explicitly mentioned should **always** be addressed in the "recommendations" or in "other_learning_paths_courses" if it is at a higher level than the user's path.

    Below is the learning path data:

    ${pathwayCoursesText.join('\n')}

    ${progressionPatternsText.join('\n')}

    IMPORTANT: Output Must only be in JSON format. Do not include any other content/explanation or formatting.

    You MUST include the following in your response:
    1. An "recommendations" array with entries for the user's current path level
    2. An "other_learning_paths_courses" array with appropriate higher-level courses
    3. A "validation_rules" object with progression information

    Each component is REQUIRED in your response.
    `;

    // Define the function schema for recommendations
    const generate_recommendations_function = {
      name: "generate_recommendations",
      description: "Generate course recommendations based on competency analysis.",
      parameters: {
        type: "object",
        properties: {
          recommendations: {
            type: "array",
            items: {
              type: "object",
              properties: {
                course: { type: "string" },
                reason: { type: "string" },
                skillArea: { type: "string" }
              },
              required: ["course", "reason", "skillArea"]
            }
          },
          other_learning_paths_courses: {
            type: "array",
            items: {
              type: "object",
              properties: {
                course: { type: "string" },
                learningPath: { type: "string" },
                reason: { type: "string" },
                skillArea: { type: "string" }
              },
              required: ["course", "learningPath", "reason", "skillArea"]
            }
          },
          validation_rules: {
            type: "object",
            properties: {
              progression_order: { type: "array", items: { type: "string" } },
              example_valid_paths: { type: "array", items: { type: "string" } },
              prohibited: { type: "string" }
            },
            required: ["progression_order", "example_valid_paths", "prohibited"]
          }
        },
        required: [
          "recommendations",
          "other_learning_paths_courses",
          "validation_rules"
        ]
      }
    };

    // Calculate tokens for recommendation API call
    const recommendationMessages = [
      { role: "system", content: recommendationInstructions },
      { role: "user", content: JSON.stringify(recommendationContext) }
    ];

    const recommendationPromptTokens = recommendationMessages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const recommendationAvailableTokens = contextWindow - recommendationPromptTokens;
    const recommendation_max_tokens = Math.min(maxResponseTokens, recommendationAvailableTokens);

    if (recommendation_max_tokens <= 0) {
      throw new Error(`Recommendation prompt exceeds context window (${recommendationPromptTokens}/${contextWindow} tokens)`);
    }

    // Improved recommendation API call - more explicit instructions and validation
    console.log('Sending recommendation request to GPT-4.1-mini...');
    const recommendationCompletion = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content: `${recommendationInstructions}\n\nCRITICAL: Your response MUST include all three required fields: "recommendations" array, "other_learning_paths_courses" array, and "validation_rules" object. Failure to include ANY of these fields is unacceptable.`
        },
        { role: "user", content: JSON.stringify(recommendationContext) }
      ],
      tools: [{
        type: "function",
        function: generate_recommendations_function
      }],
      tool_choice: { type: "function", function: { name: "generate_recommendations" } },
      max_tokens: recommendation_max_tokens
    });

    console.log('Recommendation response received from GPT-4.1-mini');

    const recommendationMessage = recommendationCompletion.choices[0].message;

    // Detailed logging and validation
    if (!recommendationMessage.tool_calls || recommendationMessage.tool_calls.length === 0) {
      console.error('ERROR: No tool calls found in recommendation response:', JSON.stringify(recommendationMessage, null, 2));
      throw new Error("Recommendation API response did not include any tool calls");
    }

    if (recommendationMessage.tool_calls[0].function.name !== "generate_recommendations") {
      console.error('ERROR: Wrong function called for recommendations:', recommendationMessage.tool_calls[0].function.name);
      throw new Error("API called the wrong function for recommendations");
    }

    // Log the arguments to debug the content
    console.log('Function arguments from recommendations:', recommendationMessage.tool_calls[0].function.arguments);

    let recommendations;
    try {
      recommendations = JSON.parse(recommendationMessage.tool_calls[0].function.arguments);

      // Verify we have the expected structure
      if (!Array.isArray(recommendations.recommendations)) {
        console.error('ERROR: Missing or invalid recommendations array:', JSON.stringify(recommendations, null, 2));
        throw new Error("API response missing required recommendations array");
      }

      if (!Array.isArray(recommendations.other_learning_paths_courses)) {
        console.error('ERROR: Missing or invalid other_learning_paths_courses array:', JSON.stringify(recommendations, null, 2));
        throw new Error("API response missing required other_learning_paths_courses array");
      }

      if (!recommendations.validation_rules || typeof recommendations.validation_rules !== 'object') {
        console.error('ERROR: Missing or invalid validation_rules object:', JSON.stringify(recommendations, null, 2));
        throw new Error("API response missing required validation_rules object");
      }

      // Verify we have some recommendations if gaps were identified
      if (recommendations.recommendations.length === 0) {
        const hasGaps = Object.values(competencyAnalysis.competencyAnalysis).some(
          area => area.gapAreas && area.gapAreas.length > 0
        );

        if (hasGaps) {
          console.error('ERROR: No recommendations provided despite identified gaps');
          throw new Error("API response contains no recommendations despite identified skill gaps");
        }
      }

      console.log('Successfully validated recommendations with',
        recommendations.recommendations.length, 'current path recommendations and',
        recommendations.other_learning_paths_courses.length, 'cross-path recommendations');

    } catch (err) {
      console.error(`Failed to parse or validate recommendations: ${err.message}`);
      throw new Error(`Invalid recommendations from API: ${err.message}`);
    }

    // Helper: Apply progression logic by encapsulating it into its own function
    function applyProgressionLogic(parsedObj) {
      // Use the dynamically generated progression chains
      const progressionChains = globalLearningPathData.progressionChains || {};

      function getNextCourses(courseTitle) {
        return progressionChains[courseTitle] || [];
      }

      function addCourse(parsedObj, courseTitle, skillArea, reason, targetPath) {
        const alreadyInRecommendations = parsedObj.recommendations.some(r => r.course === courseTitle);
        const alreadyInOther = parsedObj.other_learning_paths_courses.some(c => c.course === courseTitle);
        if (!alreadyInRecommendations && !alreadyInOther) {
          parsedObj.other_learning_paths_courses.push({
            course: courseTitle,
            learningPath: targetPath,
            reason,
            skillArea
          });
        }
      }

      function determineLearningPath(courseTitle) {
        if (courseTitle.includes("Essentials")) return "Essentials";
        if (courseTitle.includes("Intermediate")) return "Intermediate";
        if (courseTitle.includes("Advanced")) return "Advanced";
        if (courseTitle.includes("Champions")) return "Champions";
        return "Unknown";
      }

      for (const rec of parsedObj.recommendations) {
        let currentCourse = rec.course;
        let nextCourses = getNextCourses(currentCourse);
        while (nextCourses.length > 0) {
          const nextCourse = nextCourses[0];
          const reason = `To advance ${currentCourse} skills.`;
          const targetPath = determineLearningPath(nextCourse);
          addCourse(parsedObj, nextCourse, rec.skillArea, reason, targetPath);
          currentCourse = nextCourse;
          nextCourses = getNextCourses(currentCourse);
        }
      }
    }

    // Apply the progression logic helper to the parsed object
    applyProgressionLogic(recommendations);

    // Combine the competency analysis and recommendations into a single result
    // No default values - maintain exact structure with actual data
    const finalResult = {
      report: {
        competencyAnalysis: competencyAnalysis.competencyAnalysis,
        summary: competencyAnalysis.summary
      },
      recommendations: recommendations.recommendations,
      other_learning_paths_courses: recommendations.other_learning_paths_courses,
      validation_rules: recommendations.validation_rules
    };

    console.log('Successfully generated multi-skill-area recommendations with progression checks.');
    return finalResult;
  } catch (error) {
    console.error('Error in recommendation generation:', {
      error: error.message,
      role,
      learningPath,
      errorType: error.constructor.name
    });
    throw error;
  }
}

function categorizeSkillLevels(selfAssessmentResponses) {
  // Group responses by skill area
  const skillAreas = {};

  selfAssessmentResponses.forEach(response => {
    if (response.selectedAnswer === 'SKIPPED') return;

    const skillArea = response.skillArea || 'General';
    if (!skillAreas[skillArea]) {
      skillAreas[skillArea] = [];
    }

    skillAreas[skillArea].push({
      question: response.question,
      skillLevel: response.skillLevel || 1,
      selectedAnswer: response.selectedAnswer
    });
  });

  // Calculate average skill level for each area
  const skillLevelSummary = {};

  Object.entries(skillAreas).forEach(([area, responses]) => {
    if (responses.length === 0) return;

    const totalLevel = responses.reduce((sum, r) => sum + r.skillLevel, 0);
    const avgLevel = totalLevel / responses.length;

    let skillCategory;
    if (avgLevel < 1.5) {
      skillCategory = 'Basic';
    } else if (avgLevel < 2.5) {
      skillCategory = 'Intermediate';
    } else {
      skillCategory = 'Advanced';
    }

    skillLevelSummary[area] = {
      averageLevel: avgLevel,
      category: skillCategory,
      responses: responses.length
    };
  });

  return skillLevelSummary;
}


async function updateFirestoreWithRecommendations(userEmail, analysisResults, userCompany) {
  console.log('Starting Firestore update with parameters:', {
    userEmail,
    userCompany,
    hasAnalysis: !!analysisResults,
    hasOtherPathRecommendations: !!analysisResults?.other_learning_paths_courses
  });

  if (!userEmail?.trim()) {
    throw new Error('Invalid or missing user email');
  }
  if (!userCompany?.trim()) {
    throw new Error('Invalid or missing company name');
  }
  if (!analysisResults) {
    throw new Error('Missing analysis results');
  }

  const sanitizedEmail = userEmail.trim();
  const sanitizedCompany = userCompany.trim();

  try {
    // Check if company exists
    const companyDoc = await firestore
      .collection('companies')
      .doc(sanitizedCompany)
      .get();

    if (!companyDoc.exists) {
      throw new Error(`Company not found: ${sanitizedCompany}`);
    }

    // Check if user exists in that company
    const userRef = firestore
      .collection('companies')
      .doc(sanitizedCompany)
      .collection('users')
      .doc(sanitizedEmail);

    const userDoc = await userRef.get();
    if (!userDoc.exists) {
      throw new Error(`User not found in company: ${sanitizedEmail}`);
    }

    const userRole = userDoc.data().userRole ?? null;
    const currentPath = userDoc.data().currentPath ?? null;

    // Create a new assessment results document
    const assessmentResultRef = userRef.collection('assessmentResults').doc();

    // Store the complete analysis results including other learning paths
    await assessmentResultRef.set({
      competencyAnalysis: analysisResults.report.competencyAnalysis,
      analysisSummary: analysisResults.report.summary,
      courseRecommendations: analysisResults.recommendations.map(rec => ({
        courseName: rec.course,
        justification: rec.reason,
        isCurrentPath: true
      })),
      otherPathRecommendations: analysisResults.other_learning_paths_courses?.map(rec => ({
        courseName: rec.course,
        justification: rec.reason,
        learningPath: rec.learningPath,
        isCurrentPath: false
      })) || [],
      metadata: {
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        role: userRole,
        learningPath: currentPath
      }
    });

    // Update the user document with both types of recommendations
    await userRef.update({
      lastAssessmentId: assessmentResultRef.id,
      lastAssessmentDate: admin.firestore.FieldValue.serverTimestamp(),
      skillsAnalysis: analysisResults.report.summary,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),

      // Primary course recommendations (maintaining the original structure)
      courseRecommendations: analysisResults.recommendations.map(rec => ({
        course: rec.course,
        reason: rec.reason
      })),

      // Add other learning paths recommendations
      otherPathRecommendations: analysisResults.other_learning_paths_courses?.map(rec => ({
        course: rec.course,
        reason: rec.reason,
        learningPath: rec.learningPath
      })) || []
    });

    console.log('Successfully updated recommendations for:', {
      email: sanitizedEmail,
      company: sanitizedCompany,
      primaryRecsCount: analysisResults.recommendations.length,
      otherPathRecsCount: analysisResults.other_learning_paths_courses?.length || 0
    });

  } catch (error) {
    console.error('Firestore update failed:', error);
    throw new Error(`Firestore update failed: ${error.message}`);
  }
}

app.post('/api/assessment-result', async (req, res) => {
  console.log('Received assessment result:', JSON.stringify(req.body, null, 2));

  try {
    const { userEmail, role, userCompany, assessmentResult } = req.body;

    // Validate required fields
    if (!userEmail || !userCompany || !role || !assessmentResult?.learningPath) {
      console.error('Missing required fields:', { userEmail, userCompany, role, learningPath: assessmentResult?.learningPath });
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          userEmail: !userEmail ? 'Missing email' : null,
          userCompany: !userCompany ? 'Missing company' : null,
          role: !role ? 'Missing role' : null,
          learningPath: !assessmentResult?.learningPath ? 'Missing learning path' : null
        }
      });
    }

    // Ensure we have a framework before proceeding
    let framework = assessmentResult.framework;
    if (!framework) {
      console.log('No framework provided in assessment result. Attempting to fetch or generate...');
      framework = await fetchOrGenerateFramework(role);
      if (!framework) {
        return res.status(500).json({
          error: 'Unable to retrieve or generate framework. Please try again later.'
        });
      }
    }

    // Get recommendations with the provided or retrieved framework
    const analysisResults = await getRecommendationsFromOpenAIAssistant(
      role,
      assessmentResult.learningPath,
      framework,
      assessmentResult.userResponses
    );

    // Update Firestore with the complete analysis
    await updateFirestoreWithRecommendations(
      userEmail,
      analysisResults,
      userCompany
    );

    res.status(200).json({
      success: true,
      message: 'Assessment results and recommendations processed successfully',
      analysis: analysisResults
    });

  } catch (error) {
    console.error('Error in /api/assessment-result:', error);
    res.status(500).json({
      error: 'Failed to process assessment result',
      details: error.message
    });
  }
});

// Firebase Realtime Database listeners
const resultsRef = db.ref('results');
const resultsv2Ref = db.ref('resultsv2');

resultsRef.on('child_added', async (snapshot) => {
  processResult(snapshot);
});

resultsv2Ref.on('child_added', async (snapshot) => {
  processResult(snapshot, true);
});

// Helper function to process the result
const processResult = async (snapshot, isResultsV2 = false) => {
  const resultId = snapshot.key;
  const result = snapshot.val();

  const processedResultSnapshot = await processedResultsRef.child(resultId).once('value');
  if (processedResultSnapshot.exists()) {
    return;
  }

  const { email, section, score, totalQuestions, firstName, lastName, role, isNewUser, userCompany } = result;

  if (isNewUser) {
    let templateId;

    if (isResultsV2) {
      switch (section) {
        case 'Essentials': templateId = process.env.TEMPLATE_IDV2_1; break;
        case 'Intermediate': templateId = process.env.TEMPLATE_IDV2_2; break;
        case 'Advanced': templateId = process.env.TEMPLATE_IDV2_3; break;
        case 'Champions': templateId = process.env.TEMPLATE_IDV2_4; break;
        default: templateId = process.env.TEMPLATE_IDV2_1;
      }
    } else {
      switch (section) {
        case 'Essentials': templateId = process.env.TEMPLATE_ID_1; break;
        case 'Intermediate': templateId = process.env.TEMPLATE_ID_2; break;
        case 'Advanced': templateId = process.env.TEMPLATE_ID_3; break;
        case 'Champions': templateId = process.env.TEMPLATE_ID_4; break;
        default: templateId = process.env.TEMPLATE_ID_1;
      }
    }

    const scorePercentage = (score / totalQuestions) * 100;

    const dynamicData = {
      name: `${firstName} ${lastName}`,
      role,
      section,
      company: userCompany,
      score: `${scorePercentage.toFixed(0)}%`
    };

    try {
      await sendMail(email, templateId, dynamicData);
      await processedResultsRef.child(resultId).set(true);
    } catch (error) {
      console.error('Error sending email:', error);
    }
  }
};

// Updated role validation endpoint using new OpenAI API syntax
app.post('/api/validate-role', async (req, res) => {
  const { role } = req.body;
  const cacheKey = role.toLowerCase().trim();

  // Check cache first
  if (roleCache.has(cacheKey)) {
    const isValid = roleCache.get(cacheKey);
    return res.json({ isValid });
  }

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are a UK employment expert who validates job titles. Accept both traditional professions and modern job roles. A valid job title can be either an established profession or a role with a descriptive suffix. Focus on whether it represents a genuine occupation someone can be employed as.`
        },
        {
          role: 'user',
          content: `Is "${role}" a valid job title/role? Consider it valid if it meets ANY of these criteria:

1. It's an established profession or occupation (e.g., Doctor, Nurse, Architect, Teacher, Medic)
2. It's a modern job role that typically includes suffixes like:
   - Manager, Director, Coordinator, Assistant
   - Engineer, Developer, Designer
   - Specialist, Professional, Consultant
   - Coach, Trainer, Officer, Lead
3. It describes a specific job function (not a department, facility, or technology)

Examples:
✓ VALID:
- Traditional professions: "Doctor", "Architect", "Teacher", "Nurse", "Medic"
- Modern roles: "Data Manager", "Business Coach", "IT Specialist"
- Specific functions: "Accountant", "Researcher", "Analyst"

✗ INVALID:
- Departments alone: "Marketing", "Sales", "IT"
- Facilities: "Data Center", "Office"
- Generic terms: "Leadership", "Technology"
- Technologies: "Microsoft Excel", "JavaScript"

Respond ONLY with "valid" or "invalid".`
        }
      ],
      max_tokens: 1,
      temperature: 0
    });

    const assistantResponse = completion.choices[0].message.content.toLowerCase().trim();
    const isValid = assistantResponse === 'valid';

    // Store in cache
    roleCache.set(cacheKey, isValid);

    res.json({ isValid });
  } catch (error) {
    console.error('Error validating role:', error.response ? error.response.data : error.message);
    res.status(500).json({ error: 'Error validating role' });
  }
});


app.post('/api/generate-framework', async (req, res) => {
  const { role, email } = req.body;

  if (!role) {
    return res.status(400).json({ error: 'Role is required' });
  }

  console.log('Generating framework for role:', role);

  // Check if this is a student user
  if (role.toLowerCase().includes('student')) {
    console.log('Student user detected, returning predefined student framework');
    const studentFramework = getStudentFramework();

    // Cache the student framework
    try {
      await firestore.collection('frameworks').doc(role).set(studentFramework);
      console.log('Student framework cached for role:', role);
    } catch (error) {
      console.error('Error caching student framework:', error);
    }

    return res.json(studentFramework);
  }

  // Check for duplicate request
  if (email && isDuplicateRequest(email, 'generate-framework', { role })) {
    console.log(`Duplicate framework request detected for ${email}, role: ${role}`);

    // Try to return cached response
    const frameworkDoc = await firestore.collection('frameworks').doc(role).get();
    if (frameworkDoc.exists) {
      return res.json(frameworkDoc.data());
    }
  }

  // Define the function in the format expected by o3-mini
  const generate_competency_framework = {
    type: "function",
    function: {
      name: "generate_competency_framework",
      description: "Generate a competency framework for a given role.",
      parameters: {
        type: "object",
        properties: {
          role: {
            type: "object",
            properties: {
              title: { type: "string" },
              description: { type: "string" }
            },
            required: ["title", "description"]
          },
          coreCompetencies: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "string" },
                title: { type: "string" },
                requiredSkills: {
                  type: "array",
                  items: { type: "string" }
                }
              },
              required: ["id", "title", "requiredSkills"]
            }
          },
          developmentPath: {
            type: "object",
            properties: {
              levels: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    name: {
                      type: "string",
                      enum: ["Essentials", "Intermediate", "Advanced", "Champions"]
                    },
                    focus: { type: "string" },
                    outcomes: {
                      type: "array",
                      items: { type: "string" }
                    }
                  },
                  required: ["name", "focus", "outcomes"]
                }
              }
            },
            required: ["levels"]
          },
          successMetrics: {
            type: "array",
            items: { type: "string" }
          }
        },
        required: ["role", "coreCompetencies", "developmentPath", "successMetrics"]
      }
    }
  };

  try {
    // Check if a cached framework exists in Firestore
    const frameworkDoc = await firestore.collection('frameworks').doc(role).get();

    if (frameworkDoc.exists) {
      // Cached framework found, retrieve it from Firestore
      const cachedFramework = frameworkDoc.data();
      console.log('Cached framework found for role:', role);
      return res.json(cachedFramework);
    }

    // Make sure learning path data is loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        return res.status(500).json({ error: 'Learning path data could not be loaded' });
      }
    }

    // Prepare the course catalog information
    const categoryInfo = [];

    // Format the course data for the prompt
    Object.keys(globalLearningPathData.coursesByCategory).forEach(category => {
      const courses = globalLearningPathData.coursesByCategory[category];

      // Group by pathway
      const coursesByPathway = {};
      courses.forEach(course => {
        if (!coursesByPathway[course.pathway]) {
          coursesByPathway[course.pathway] = [];
        }
        coursesByPathway[course.pathway].push(course.title);
      });

      // Create formatted category string
      let categoryText = `${category.toUpperCase()}:\n`;
      Object.keys(coursesByPathway).forEach(pathway => {
        const pathwayCourses = coursesByPathway[pathway];
        pathwayCourses.forEach(course => {
          categoryText += `- "${course}"\n`;
        });
      });

      categoryInfo.push(categoryText);
    });

    const coursesCatalogText = categoryInfo.join('\n');

    // Calculate token usage to ensure we're within model limits
    const { encode } = require('gpt-tokenizer');
    const messages = [
      {
        role: "system",
        content: "You are a role analysis expert. Create a competency framework that maps job competencies to our learning catalog."
      },
      {
        role: "user",
        content: `For the role of "${role}", create a competency framework by identifying the core competencies required for this role. For each competency, map only the necessary courses from our learning catalog that are directly relevant to developing that competency.

        Available courses in our learning catalog:

        ${coursesCatalogText}

        Important Instructions:
        - Only include courses that are essential for developing each competency.
        - Do not include unnecessary courses that do not directly contribute to the competency or daily work for the role.
        - In the "requiredSkills" array, for each course included, provide a brief explanation of why it is linked to the competency or how it will help the person excel. Format each entry as "Course Name - Brief Explanation".
        - Ensure the output JSON exactly matches the specified structure.
        - You must only recommend courses that exist in the catalog above, with exact matching names.`
      }
    ];

    const promptTokens = messages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 200000; // o3-mini's context size
    const maxResponseTokens = 100000; // o3-mini's max output
    const availableTokens = contextWindow - promptTokens;

    if (availableTokens <= 0) {
      throw new Error(`Prompt exceeds context window (${promptTokens}/${contextWindow} tokens)`);
    }

    // Cached framework not found, generate a new one using OpenAI API with o3-mini
    const completion = await openai.chat.completions.create({
      model: "o3-mini",
      messages: [
        {
          role: "system",
          content: "You are a role analysis expert. Create a competency framework that maps job competencies to our learning catalog."
        },
        {
          role: "user",
          content: `For the role of "${role}", create a competency framework by identifying the core competencies required for this role. For each competency, map only the necessary courses from our learning catalog that are directly relevant to developing that competency.

          Available courses in our learning catalog:

          ${coursesCatalogText}

          Important Instructions:
          - Only include courses that are essential for developing each competency.
          - Do not include unnecessary courses that do not directly contribute to the competency or daily work for the role.
          - In the "requiredSkills" array, for each course included, provide a brief explanation of why it is linked to the competency or how it will help the person excel. Format each entry as "Course Name - Brief Explanation".
          - Ensure the output JSON exactly matches the specified structure.
          - You must only recommend courses that exist in the catalog above, with exact matching names.`
        }
      ],
      tools: [generate_competency_framework],
      tool_choice: { type: "function", function: { name: "generate_competency_framework" } },
      max_completion_tokens: Math.min(maxResponseTokens, availableTokens),
      reasoning_effort: "low" // Using low reasoning effort as requested
    });

    const message = completion.choices[0].message;

    if (message.tool_calls && message.tool_calls.length > 0 &&
        message.tool_calls[0].function.name === "generate_competency_framework") {

      const functionArgs = JSON.parse(message.tool_calls[0].function.arguments);

      // Validate pathway names
      const validPathways = new Set(["Essentials", "Intermediate", "Advanced", "Champions"]);
      const pathwaysAreValid = functionArgs.developmentPath.levels.every((level) =>
        validPathways.has(level.name)
      );

      if (!pathwaysAreValid) {
        throw new Error("Invalid pathway names detected");
      }

      // Cache the result in Firestore
      await firestore.collection('frameworks').doc(role).set(functionArgs);

      // Track this request to prevent duplicates
      if (email) {
        trackRequest(email, 'generate-framework', { role });
      }

      console.log('Generated and cached framework for role:', role);
      res.json(functionArgs);
    } else {
      throw new Error("Assistant did not call the expected function");
    }
  } catch (error) {
    console.error("Error generating framework:", error);
    // Release the request if it fails
    if (email) {
      releaseRequest(email, 'generate-framework', { role });
    }
    res.status(500).json({ error: "Failed to generate framework" });
  }
});

app.post('/api/generate-quiz', async (req, res) => {
  console.log('Received quiz generation request:', {
    role: req.body.role,
    section: req.body.section,
    email: req.body.email, // Log email for debugging
    isStudent: req.body.role?.toLowerCase().includes('student')
  });

  const { role, section, framework, email } = req.body;

  if (!role || !section || !framework) {
    console.log('Missing required fields:', { role, section, framework: !!framework });
    return res.status(400).json({
      error: 'Missing required fields',
      details: {
        role: !role ? 'Missing role' : undefined,
        section: !section ? 'Missing section' : undefined,
        framework: !framework ? 'Missing framework' : undefined
      }
    });
  }

  // Check for duplicate request
  if (email && isDuplicateRequest(email, 'generate-quiz', { role, section })) {
    console.log(`Duplicate quiz request detected for ${email}`);

    // Try to return cached questions
    const cachedQuestions = await getCachedQuestions(role, section);
    if (cachedQuestions?.length === 10) {
      return res.json(cachedQuestions);
    }
  }

  try {
    // Check cache first
    const cachedQuestions = await getCachedQuestions(role, section);
    if (cachedQuestions?.length === 10) {
      console.log('Retrieved questions from cache for:', { role, section });
      return res.json(cachedQuestions);
    }

    // Ensure we have the global learning path data loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded for quiz generation, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        throw new Error('Learning path data could not be loaded');
      }
    }

    console.log('No cached questions found, generating new questions');
    const difficultyLevel = getDifficultyLevel(section);

    // Check if this is a student user and handle differently
    const isStudent = role.toLowerCase().includes('student');
    let courses;

    if (isStudent) {
      console.log('Student user detected, using student-specific course extraction');
      courses = extractStudentCoursesForSection(section, globalLearningPathData);
      console.log('Student courses extracted:', courses);

      // Fallback for students if no courses found
      if (courses.length === 0) {
        console.log('No student courses found, using fallback courses');
        courses = ['Computer Skills – Beginners', 'Computer Skills – Beginners Plus'];
      }
    } else {
      // Extract relevant courses for the role and section (professional users)
      const relevantCourses = extractRelevantCoursesForRoleAndSection(role, section, framework, globalLearningPathData);
      // If we don't have enough relevant courses, fall back to extracting from framework
      courses = relevantCourses.length >= 3 ? relevantCourses : extractCoursesFromFramework(framework);

      // Final validation for professional users
      if (courses.length === 0) {
        console.warn('No courses found for question generation');
        return res.status(400).json({ error: 'No relevant courses found for this role and section' });
      }
    }

    // High-quality example questions with diverse scenario formats
    let exampleQuestions;

    if (isStudent) {
      // Student-focused example questions
      exampleQuestions = [
        {
          question: "What is the first step when turning on a computer for the first time?",
          options: [
            "Open a web browser",
            "Press the power button",
            "Connect to WiFi",
            "Create a user account"
          ],
          answer: "Press the power button",
          course: "Computer Skills – Beginners"
        },
        {
          question: "Which key combination is used to copy text in most applications?",
          options: [
            "Ctrl + V",
            "Ctrl + C",
            "Ctrl + X",
            "Ctrl + Z"
          ],
          answer: "Ctrl + C",
          course: "Computer Skills – Beginners Plus"
        },
        {
          question: "When you want to save a file you've been working on, what should you do?",
          options: [
            "Turn off the computer immediately",
            "Close the program without saving",
            "Use Ctrl + S or click the Save button",
            "Delete the file"
          ],
          answer: "Use Ctrl + S or click the Save button",
          course: "Computer Skills – Improvers Plus"
        }
      ];
    } else {
      // Professional-focused example questions
      exampleQuestions = [
        {
          question: "Which feature should be configured to ensure only team leads can post announcements in a specific channel?",
          options: [
            "Channel notifications",
            "Channel moderation settings",
            "Channel email address",
            "Channel tabs"
          ],
          answer: "Channel moderation settings",
          course: "Teams - Channels"
        },
        {
          question: "During a large presentation meeting, what setting prevents attendees from unmuting themselves?",
          options: [
            "Allow mic for attendees",
            "Who can present?",
            "Meeting recording",
            "Live reactions"
          ],
          answer: "Who can present?",
          course: "Teams - Meetings"
        },
        {
          question: "When a suspicious file is detected, which Defender feature automatically quarantines it?",
          options: [
            "AutoInvestigate",
            "Threat & Vulnerability Management",
            "Real-time protection",
            "Secure Score"
          ],
          answer: "Real-time protection",
          course: "Microsoft Defender"
        }
      ];
    }

    // Calculate target distribution for balanced questions
    const targetDistribution = calculateTargetDistribution(courses);

    // Calculate token usage for the prompt
    const { encode } = require('gpt-tokenizer');

    // Create appropriate prompt based on user type
    let diversePrompt;

    if (isStudent) {
      // Student-focused prompt
      diversePrompt = `You are an expert assessment designer specializing in digital literacy and computer skills evaluation for students. Your goal is to create clear, educational questions that assess fundamental computer and digital skills without being intimidating.

STUDENT ASSESSMENT DESIGN PRINCIPLES:

1. CLEAR AND SIMPLE LANGUAGE:
   - Use straightforward, easy-to-understand language
   - Avoid technical jargon unless it's being taught
   - Focus on practical, everyday computer tasks
   - Make questions relevant to academic and personal use

2. DIVERSE QUESTION FORMATS:
   - "What should you do when..."
   - "Which option is used to..."
   - "How do you..."
   - "What happens when you..."
   - "Which key combination..."

3. EDUCATIONAL FOCUS:
   - Questions should teach as well as assess
   - Focus on fundamental skills that build confidence
   - Include basic computer operations, file management, and common applications
   - Cover digital safety and responsible technology use

4. ANSWER OPTIONS DESIGN:
   - The correct answer should be clearly the best choice
   - Wrong answers should be obviously incorrect to confident users
   - Include common beginner mistakes as distractors
   - Use simple, clear terminology

5. SKILL PROGRESSION: Questions should match the learning level (${difficultyLevel}) and help students build from basic to more advanced skills.

Here are examples of excellent student assessment questions:

${JSON.stringify(exampleQuestions, null, 2)}`;
    } else {
      // Professional-focused prompt
      diversePrompt = `You are an expert assessment designer specializing in Microsoft 365 and cybersecurity skills evaluation. Your goal is to create varied, scenario-based questions that accurately assess specific skills without being repetitive.

ASSESSMENT DESIGN PRINCIPLES:

1. DIVERSE QUESTION FORMATS:
   - DO NOT start every question with "[Role] needs to..." or "[Role] wants to..."
   - Instead, use a variety of sentence structures:
     * "Which feature allows users to..."
     * "What is the most efficient way to..."
     * "To accomplish [task], which setting should be used?"
     * "When [situation occurs], what is the appropriate action?"

2. CLEAR PROBLEM STATEMENT:
   - Focus on the task or problem itself, not the person doing it
   - Make the scenario relevant to a ${role}'s work without repeatedly mentioning the role
   - Use active voice and direct questions

3. ANSWER OPTIONS DESIGN:
   - The correct answer should directly solve the scenario problem
   - Distractors (wrong answers) must be plausible alternatives that:
     * Reflect common misconceptions
     * Use correct terminology but apply it incorrectly
     * Represent suboptimal approaches that seem reasonable

4. QUESTION PRECISION:
   - Questions should be precise and clear
   - Include enough context for the scenario to make sense
   - No arbitrary length restrictions - use as many words as needed to clearly present the scenario

5. DIFFERENTIATION: Questions should distinguish between basic familiarity and mastery of tools.

Here are examples of excellent assessment questions with diverse formats:

${JSON.stringify(exampleQuestions, null, 2)}`;
    }

    const baseMessages = [
      {
        role: "system",
        content: diversePrompt
      },
      {
        role: "user",
        content: `Create 10 high-quality assessment questions relevant to a ${role} at the ${difficultyLevel} level.

IMPORTANT: Use varied question formats! DO NOT begin questions with "${role}..." or similar patterns.

Ensure each question:
1. Presents a specific workplace scenario using varied sentence structures and formats
2. Tests a distinct skill from the courses relevant to this role
3. Has one clearly correct answer and three plausible but incorrect options
4. Is precise and as detailed as necessary to clearly present the scenario

The questions should cover these skill areas evenly: ${courses.join(', ')}.`
      }
    ];

    const promptTokens = baseMessages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 200000; // o3-mini's context size
    const maxResponseTokens = 12000; // Allowing more tokens for quality responses
    const availableTokens = contextWindow - promptTokens;

    if (availableTokens <= 0) {
      throw new Error(`Prompt exceeds context window (${promptTokens}/${contextWindow} tokens)`);
    }

    // Define the generate_quiz_questions function for o3-mini
    const generate_quiz_questions = {
      type: "function",
      function: {
        name: "generate_quiz_questions",
        description: "Generate high-quality assessment questions",
        parameters: {
          type: "object",
          properties: {
            questions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  question: {
                    type: "string",
                    description: "A precise scenario-based question with varied format (not always starting with the role)"
                  },
                  options: {
                    type: "array",
                    items: { type: "string" },
                    minItems: 4,
                    maxItems: 4,
                    description: "Four plausible options with one correct answer"
                  },
                  answer: {
                    type: "string",
                    description: "The correct answer (must match exactly one of the options)"
                  },
                  course: {
                    type: "string",
                    description: "The specific skill area or course being tested"
                  }
                },
                required: ["question", "options", "answer", "course"]
              }
            }
          },
          required: ["questions"]
        }
      }
    };

    let allQuestions = [];
    let retryCount = 0;
    const maxRetries = 2;

    while (allQuestions.length < 10 && retryCount < maxRetries) {
      console.log(`Attempt ${retryCount + 1}: Generating diverse questions with o3-mini`);

      // Adaptive prompting based on what's already been generated
      const targetCount = 10 - allQuestions.length;
      const missingCourses = courses.filter(c =>
        !allQuestions.some(q => q.course && q.course.toLowerCase().includes(c.toLowerCase()))
      );

      let userPrompt;

      if (isStudent) {
        userPrompt = retryCount === 0
          ? `Create ${targetCount} high-quality digital literacy assessment questions for students at the ${difficultyLevel} level.

             IMPORTANT: Use clear, simple language appropriate for students learning computer skills.
             Focus on practical, everyday computer tasks and academic applications.

             Question topics should cover these skill areas: ${courses.join(', ')}.

             Make questions educational and confidence-building:
             - Focus on fundamental computer operations
             - Include basic Microsoft Office applications for schoolwork
             - Cover digital safety and responsible technology use
             - Use scenarios relevant to student life and learning

             Ensure questions are appropriate for the ${difficultyLevel} level of digital literacy.`
          : `Create ${targetCount} additional student-focused assessment questions for these specific skill areas:
             ${missingCourses.join(', ')}.

             IMPORTANT: Keep language simple and educational. Focus on practical skills students need for academic success.
             Make questions clear and confidence-building for learners developing digital literacy.`;
      } else {
        userPrompt = retryCount === 0
          ? `Create ${targetCount} high-quality assessment questions for a ${role} at the ${difficultyLevel} level.

             CRUCIAL: Use VARIED question formats. DO NOT begin every question with "${role}..." or similar patterns.
             Instead, focus directly on the tasks, tools, or problems. For example:
             - "Which Excel feature should be used to..."
             - "When creating a SharePoint site, what permission setting..."
             - "To protect sensitive data in an email attachment, which method..."

             Make questions as precise as needed - there is no length restriction. Include enough context for the scenario to be clear.

             Focus on testing skills from these areas: ${courses.join(', ')}.`
          : `Create ${targetCount} additional assessment questions focusing specifically on these skill areas:
             ${missingCourses.join(', ')}.

             IMPORTANT: Review and vary your question formats! DO NOT start questions with "${role}..."
             Instead, vary your approach and focus on the tasks/problems directly.

             Make questions as detailed as necessary to present clear scenarios.`;
      }

      const completion = await openai.chat.completions.create({
        model: "o3-mini",
        messages: [
          {
            role: "system",
            content: diversePrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        tools: [generate_quiz_questions],
        tool_choice: { type: "function", function: { name: "generate_quiz_questions" } },
        max_completion_tokens: Math.min(maxResponseTokens, availableTokens),
        reasoning_effort: "medium"
      });

      try {
        const message = completion.choices[0].message;

        if (message.tool_calls && message.tool_calls.length > 0 &&
            message.tool_calls[0].function.name === "generate_quiz_questions") {

          const newQuestions = JSON.parse(message.tool_calls[0].function.arguments).questions;

          // Process and normalize questions
          newQuestions.forEach(q => {
            // Normalize course names if needed
            q.course = normalizeCourse(q.course, courses);

            // Convert options to simple array format if they came in with A, B, C, D format
            if (Array.isArray(q.options) && q.options.length === 4) {
              // If options start with A., B., etc., remove those prefixes
              q.options = q.options.map(opt =>
                opt.replace(/^[A-D]\.\s*/, '').replace(/^[A-D]\)\s*/, '')
              );

              // If answer has A., B., etc. prefix, convert to the actual answer text
              if (/^[A-D]\.?$/.test(q.answer)) {
                const index = q.answer.toUpperCase().charCodeAt(0) - 65; // Convert A->0, B->1, etc.
                if (index >= 0 && index < q.options.length) {
                  q.answer = q.options[index];
                }
              }
            }

            // Basic validation (minimal to ensure question works)
            if (!q.options || q.options.length !== 4) {
              q.options = ["Option A", "Option B", "Option C", "Option D"];
              q.answer = "Option A";
            }

            // Make sure answer is one of the options
            if (!q.options.includes(q.answer)) {
              q.answer = q.options[0];
            }
          });

          // Add new questions, avoiding exact duplicates
          newQuestions.forEach(q => {
            if (!allQuestions.some(existing => existing.question === q.question)) {
              allQuestions.push(q);
            }
          });

          // Trim to 10 questions if we have more, ensuring balanced distribution
          if (allQuestions.length > 10) {
            allQuestions = balanceQuestionsAcrossCourses(allQuestions, targetDistribution).slice(0, 10);
          }
        }
      } catch (parseError) {
        console.warn('Error parsing questions:', parseError);
      }

      retryCount++;
    }

    // If we still don't have enough questions, generate backup questions
    if (allQuestions.length < 10) {
      console.log('Generating backup questions to reach 10 total');
      const remainingQuestions = await generateDiverseBackupQuestions(
        10 - allQuestions.length,
        section,
        role,
        exampleQuestions,
        diversePrompt,
        isStudent
      );
      allQuestions.push(...remainingQuestions);
    }

    // Final format cleanup
    allQuestions = allQuestions.map(q => ({
      question: q.question,
      options: q.options,
      answer: q.answer,
      course: q.course
    })).slice(0, 10);

    // Cache the questions if we have a full set
    if (allQuestions.length === 10) {
      try {
        const cacheKey = `${role}_${section}_questions`;
        await firestore.collection('questionCache').doc(cacheKey).set({
          questions: allQuestions,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('Successfully cached questions for:', { role, section });
      } catch (cacheError) {
        console.warn('Failed to cache questions:', cacheError);
      }
    }

    // Track this request to prevent duplicates
    if (email) {
      trackRequest(email, 'generate-quiz', { role, section });
    }

    res.json(allQuestions);

  } catch (error) {
    console.error('Error generating quiz:', error);
    // Release the request if it fails
    if (email) {
      releaseRequest(email, 'generate-quiz', { role, section });
    }
    res.status(500).json({
      error: 'Failed to generate quiz questions',
      details: error.message
    });
  }
});

// Helper function for extracting student-specific courses for a section
function extractStudentCoursesForSection(section, learningPathData) {
  console.log('Extracting student courses for section:', section);

  // Define student-specific course mappings based on the computer skills classification
  const studentCoursesBySection = {
    'essentials': [
      'Computer Skills – Beginners',
      'Computer Skills – Beginners Plus'
    ],
    'intermediate': [
      'Computer Skills – Improvers Plus',
      'Computer Skills for Everyday Life – Level 1'
    ],
    'advanced': [
      'ICDL Level 2',
      'Computer Skills for Work – Level 2'
    ],
    'champions': [
      'ICDL Level 3'
    ]
  };

  const sectionKey = section.toLowerCase();
  const targetCourses = studentCoursesBySection[sectionKey] || studentCoursesBySection['essentials'];

  // Find matching courses in the learning path data
  const matchedCourses = [];

  targetCourses.forEach(targetCourse => {
    // Look for exact matches or partial matches in the learning path data
    const matches = learningPathData.allCourses.filter(course => {
      const courseTitle = course.title.toLowerCase();
      const target = targetCourse.toLowerCase();

      // Check for exact match or if the course title contains the target
      return courseTitle === target ||
             courseTitle.includes(target) ||
             target.includes(courseTitle.split(' - ')[0].toLowerCase());
    });

    if (matches.length > 0) {
      // Prefer courses from the appropriate pathway level
      const pathwayPriority = {
        'essentials': 'Essentials Learning Pathway',
        'intermediate': 'Intermediate Learning Pathway',
        'advanced': 'Advanced Learning Pathway',
        'champions': 'Champions Learning Pathway'
      };

      const preferredPathway = pathwayPriority[sectionKey];
      const preferredMatch = matches.find(m => m.pathway === preferredPathway);

      if (preferredMatch) {
        matchedCourses.push(preferredMatch.title);
      } else {
        matchedCourses.push(matches[0].title);
      }
    } else {
      // If no exact match, add the target course name for question generation
      matchedCourses.push(targetCourse);
    }
  });

  console.log('Student courses found for section', section, ':', matchedCourses);
  return matchedCourses;
}

// Helper function for extracting relevant courses for a role and section
function extractRelevantCoursesForRoleAndSection(role, section, framework, learningPathData) {
  const relevantCourses = new Set();
  const competencyKeywords = new Set();
  const roleKeywords = role.toLowerCase().split(/\s+/);

  // Map section to pathway
  let targetPathway;
  switch(section.toLowerCase()) {
    case 'essentials':
    case 'foundational':
      targetPathway = 'Essentials Learning Pathway';
      break;
    case 'intermediate':
      targetPathway = 'Intermediate Learning Pathway';
      break;
    case 'advanced':
      targetPathway = 'Advanced Learning Pathway';
      break;
    case 'champions':
    case 'expert':
      targetPathway = 'Champions Learning Pathway';
      break;
    default:
      targetPathway = 'Essentials Learning Pathway';
  }

  try {
    // First, get all courses from the target pathway
    const pathwayCourses = learningPathData.coursesByPathway[targetPathway] || [];
    pathwayCourses.forEach(course => relevantCourses.add(course.title));

    // Next, extract courses from the framework that are relevant to the role
    if (framework?.coreCompetencies) {
      framework.coreCompetencies.forEach(competency => {
        // Extract competency title as a keyword
        competencyKeywords.add(competency.title.toLowerCase());

        if (competency.requiredSkills) {
          competency.requiredSkills.forEach(skill => {
            // Extract course names from skills (typically in format "Course Name - Explanation")
            const courseMatch = skill.match(/^([^-]+)\s*-/);
            if (courseMatch && courseMatch[1]) {
              const courseName = courseMatch[1].trim();

              // Find exact or similar course in the learning path data
              const exactMatches = learningPathData.allCourses.filter(c =>
                c.title === courseName || c.title.includes(courseName)
              );

              exactMatches.forEach(match => {
                if (match.pathway === targetPathway) {
                  relevantCourses.add(match.title);
                }
              });
            }

            // Extract subtopics from required skills
            const skillParts = skill.split('-');
            if (skillParts.length > 1) {
              competencyKeywords.add(skillParts[1].trim().toLowerCase());
            }
          });
        }
      });
    }
  } catch (error) {
    console.warn('Error extracting relevant courses:', error);
  }

  return {
    courses: Array.from(relevantCourses),
    competencyKeywords: Array.from(competencyKeywords),
    roleKeywords
  };
}

// Normalize course name to match one of the target courses
function normalizeCourse(courseName, targetCourses) {
  if (!courseName) return 'General';

  // Check for direct matches or substrings
  for (const target of targetCourses) {
    if (courseName.toLowerCase().includes(target.toLowerCase())) {
      return target;
    }
  }

  // Look for partial matches
  const words = courseName.toLowerCase().split(/\W+/);
  for (const target of targetCourses) {
    const targetWords = target.toLowerCase().split(/\W+/);
    if (targetWords.some(tw => words.includes(tw))) {
      return target;
    }
  }

  return courseName;
}

// Balance questions across courses more effectively
function balanceQuestionsAcrossCourses(questions, targetDistribution) {
  // Group questions by course
  const courseGroups = {};
  questions.forEach(q => {
    const course = q.course || 'General';
    courseGroups[course] = courseGroups[course] || [];
    courseGroups[course].push(q);
  });

  // Determine how many questions we want from each course
  const coursesNeeded = Object.keys(targetDistribution);
  const idealCount = Math.min(10, questions.length) / coursesNeeded.length;

  // For each course, keep the best N questions (where N is based on target distribution)
  const balanced = [];

  // First pass: take the minimum number from each course to ensure coverage
  for (const course of coursesNeeded) {
    const groupQuestions = courseGroups[course] || [];
    const targetCount = Math.ceil(targetDistribution[course] || idealCount);
    const takeCount = Math.min(groupQuestions.length, targetCount);

    if (takeCount > 0) {
      balanced.push(...groupQuestions.slice(0, takeCount));
      courseGroups[course] = groupQuestions.slice(takeCount);
    }
  }

  // Second pass: fill remaining slots with leftover questions
  const remaining = 10 - balanced.length;
  if (remaining > 0) {
    const leftover = Object.values(courseGroups).flat();
    balanced.push(...leftover.slice(0, remaining));
  }

  return balanced;
}

// Generate backup questions with diverse formats
async function generateDiverseBackupQuestions(count, section, role, _, diversePrompt, isStudent = false) {
  try {
    // Define the generate_quiz_questions function
    const generate_quiz_questions = {
      type: "function",
      function: {
        name: "generate_quiz_questions",
        description: "Generate diverse format assessment questions",
        parameters: {
          type: "object",
          properties: {
            questions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  question: { type: "string" },
                  options: {
                    type: "array",
                    items: { type: "string" },
                    minItems: 4,
                    maxItems: 4
                  },
                  answer: { type: "string" },
                  course: { type: "string" }
                },
                required: ["question", "options", "answer", "course"]
              }
            }
          },
          required: ["questions"]
        }
      }
    };

    let userPrompt;
    if (isStudent) {
      userPrompt = `Create ${count} digital literacy assessment questions for students at the ${section} level.

        IMPORTANT: Use clear, simple language appropriate for students learning computer skills.
        Focus on fundamental computer operations, basic Microsoft Office applications, and digital safety.

        Each question should:
        - Use straightforward, easy-to-understand language
        - Focus on practical, everyday computer tasks
        - Be relevant to academic and personal use
        - Have 4 answer options with one clearly correct answer
        - Test basic digital literacy skills appropriate for the ${section} level

        Question formats should vary and include:
        - "What should you do when..."
        - "Which option is used to..."
        - "How do you..."
        - "What happens when you..."`;
    } else {
      userPrompt = `Create ${count} assessment questions focusing on essential Microsoft 365 and cybersecurity skills.

        CRUCIAL INSTRUCTION: Use VARIED question formats. DO NOT begin questions with "${role}..."
        Instead, focus on the tasks, tools, or problems directly with questions like:
        - "What is the correct method to..."
        - "Which setting enables..."
        - "To resolve [specific problem], what action should be taken?"

        Make questions as precise and detailed as needed - there is no length restriction.

        Each question should:
        - Have 4 answer options with only one correct answer
        - Test practical knowledge applicable to the ${section} level
        - Be clear and focused on specific skills`;
    }

    const completion = await openai.chat.completions.create({
      model: "o3-mini",
      messages: [
        {
          role: "system",
          content: diversePrompt || `Create diverse format assessment questions that vary in structure and don't repeatedly mention the role.`
        },
        {
          role: "user",
          content: userPrompt
        }
      ],
      tools: [generate_quiz_questions],
      tool_choice: { type: "function", function: { name: "generate_quiz_questions" } },
      max_completion_tokens: 5000,
      reasoning_effort: "high" // Using high reasoning for best quality
    });

    const message = completion.choices[0].message;

    if (message.tool_calls && message.tool_calls.length > 0 &&
        message.tool_calls[0].function.name === "generate_quiz_questions") {

      const backupQuestions = JSON.parse(message.tool_calls[0].function.arguments).questions;

      // Process questions without strict validation
      backupQuestions.forEach(q => {
        // Normalize options if needed
        if (Array.isArray(q.options) && q.options.length === 4) {
          q.options = q.options.map(opt =>
            opt.replace(/^[A-D]\.\s*/, '').replace(/^[A-D]\)\s*/, '')
          );

          if (/^[A-D]\.?$/.test(q.answer)) {
            const index = q.answer.toUpperCase().charCodeAt(0) - 65;
            if (index >= 0 && index < q.options.length) {
              q.answer = q.options[index];
            }
          }
        }

        // Basic validation fixes
        if (!q.options || q.options.length !== 4) {
          q.options = ["Option A", "Option B", "Option C", "Option D"];
          q.answer = "Option A";
        }

        // Make sure answer is one of the options
        if (!q.options.includes(q.answer)) {
          q.answer = q.options[0];
        }

        // Ensure course is set
        if (!q.course) {
          q.course = 'General';
        }
      });

      return backupQuestions.slice(0, count);
    }

    return [];
  } catch (error) {
    console.warn('Error generating backup questions:', error);
    return [];
  }
}


function calculateTargetDistribution(courses, totalQuestions = 10) {
  const distribution = {};
  try {
    const baseQuestionsPerCourse = Math.floor(totalQuestions / courses.length);
    const remainingQuestions = totalQuestions % courses.length;

    courses.forEach((course, index) => {
      distribution[course] = baseQuestionsPerCourse;
      if (index < remainingQuestions) {
        distribution[course]++;
      }
    });
  } catch (error) {
    console.warn('Error calculating distribution:', error);
    courses.forEach(course => {
      distribution[course] = Math.floor(totalQuestions / courses.length);
    });
  }
  return distribution;
}


// Function to generate student-focused framework
function getStudentFramework() {
  return {
    role: {
      title: "Student - Digital Skills Development",
      description: "A comprehensive framework for students to develop essential digital literacy and Microsoft 365 skills for academic success and future career preparation."
    },
    coreCompetencies: [
      {
        id: "fundamental-computer-operations",
        title: "Fundamental Computer Operations",
        requiredSkills: [
          "Computer Skills – Beginners - Essentials - Learn fundamental computer operations including switching on/off, logging in, keyboard and mouse usage, and safely opening/closing programs",
          "Computer Skills – Beginners Plus - Essentials - Build on basic skills with text document creation, internet usage, email setup, and mobile technology basics"
        ]
      },
      {
        id: "enhanced-computer-skills",
        title: "Enhanced Computer Skills",
        requiredSkills: [
          "Computer Skills – Improvers Plus - Intermediate - Enhance software skills including file management, online searching, and digital safety",
          "Computer Skills for Everyday Life – Level 1 - Intermediate - Improve skills using Microsoft applications, understand websites, and internet banking"
        ]
      },
      {
        id: "professional-computer-skills",
        title: "Professional Computer Skills",
        requiredSkills: [
          "ICDL Level 2 - Advanced - Enhance competence in word processing, spreadsheet, and presentation software for office roles",
          "Computer Skills for Work – Level 2 - Advanced - Focus on workplace-specific word processing, spreadsheet, and presentation skills"
        ]
      },
      {
        id: "advanced-computer-skills",
        title: "Advanced Computer Skills",
        requiredSkills: [
          "ICDL Level 3 - Champions - Advanced Microsoft applications knowledge for further education and IT careers"
        ]
      }
    ],
    developmentPath: {
      levels: [
        {
          name: "Essentials",
          focus: "Foundation Skills",
          outcomes: [
            "Master basic computer operations and navigation",
            "Create and edit simple documents, spreadsheets, and presentations",
            "Use email and basic online communication tools effectively",
            "Understand fundamental digital safety practices"
          ]
        },
        {
          name: "Intermediate",
          focus: "Academic Application",
          outcomes: [
            "Apply Microsoft Office skills to academic projects and assignments",
            "Collaborate effectively using digital tools and platforms",
            "Organize and manage digital files and resources efficiently",
            "Demonstrate responsible digital citizenship"
          ]
        },
        {
          name: "Advanced",
          focus: "Professional Preparation",
          outcomes: [
            "Use advanced features in Microsoft Office applications",
            "Create professional-quality documents and presentations",
            "Understand workplace digital communication standards",
            "Apply advanced cybersecurity practices"
          ]
        },
        {
          name: "Champions",
          focus: "Digital Leadership",
          outcomes: [
            "Mentor others in digital skills development",
            "Innovate with advanced digital tools and technologies",
            "Lead digital projects and initiatives",
            "Prepare for technology-focused career paths"
          ]
        }
      ]
    },
    successMetrics: [
      "Successful completion of basic computer skills assessments",
      "Ability to create academic documents using Microsoft Office",
      "Effective participation in digital collaboration activities",
      "Demonstration of safe and responsible digital practices",
      "Progression through learning pathway levels",
      "Application of digital skills in academic and personal contexts"
    ]
  };
}

function extractCoursesFromFramework(framework) {
  const courses = new Set();
  try {
    if (Array.isArray(framework.coreCompetencies)) {
      framework.coreCompetencies.forEach(competency => {
        if (Array.isArray(competency.requiredSkills)) {
          competency.requiredSkills.forEach(skill => {
            const courseName = skill.split('-')[0].trim();
            if (courseName) courses.add(courseName);
          });
        }
      });
    }
  } catch (error) {
    console.warn('Error extracting courses:', error);
  }
  return Array.from(courses);
}

function getDifficultyLevel(section) {
  switch (section.toLowerCase()) {
    case 'foundational':
      return 'moderately challenging - focus on practical application of fundamental concepts with carefully crafted distractors. Questions should test understanding rather than mere recall, incorporating realistic scenarios and plausible alternative options.';
    case 'intermediate':
      return 'challenging - present complex scenarios that require combining multiple concepts. Include subtle distinctions between options and test deeper understanding of feature interactions and best practices.';
    case 'advanced':
      return 'very challenging - focus on sophisticated problem-solving, edge cases, and strategic decision-making. Questions should require deep understanding of advanced features, system interactions, and optimization considerations.';
    case 'expert':
      return 'highly challenging - test mastery through complex scenarios involving multiple interconnected concepts, strategic planning, and innovative problem-solving. Questions should require comprehensive understanding and the ability to evaluate trade-offs between different approaches.';
    default:
      return 'challenging - include realistic scenarios that test practical application and understanding';
  }
}

async function getCachedQuestions(role, section) {
  try {
    const cacheKey = `${role}_${section}_questions`;
    const cached = await firestore.collection('questionCache').doc(cacheKey).get();

    if (cached.exists) {
      const data = cached.data();
      const cacheAge = Date.now() - data.timestamp.toDate();
      const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      if (cacheAge < CACHE_TTL) {
        console.log(`Using cached questions for ${role}/${section}, cache age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
        return data.questions;
      } else {
        console.log(`Cache expired for ${role}/${section}, age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
      }
    }
    return null;
  } catch (error) {
    console.warn('Cache retrieval failed:', error);
    return null;
  }
}


// Add this new endpoint to server.js
app.post('/api/generate-self-assessment', async (req, res) => {
  console.log('Received self-assessment generation request:', {
    role: req.body.role,
    section: req.body.section,
    email: req.body.email // Log email for debugging
  });

  const { role, section, framework, email } = req.body;

  if (!role || !section || !framework) {
    console.log('Missing required fields:', { role, section, framework: !!framework });
    return res.status(400).json({
      error: 'Missing required fields',
      details: {
        role: !role ? 'Missing role' : undefined,
        section: !section ? 'Missing section' : undefined,
        framework: !framework ? 'Missing framework' : undefined
      }
    });
  }

  // Check for duplicate request
  if (email && isDuplicateRequest(email, 'generate-self-assessment', { role, section })) {
    console.log(`Duplicate self-assessment request detected for ${email}`);

    // Try to return cached questions
    const cachedQuestions = await getCachedSelfAssessmentQuestions(role, section);
    if (cachedQuestions?.length === 5) {
      return res.json(cachedQuestions);
    }
  }

  try {
    // Check cache first
    const cachedQuestions = await getCachedSelfAssessmentQuestions(role, section);
    if (cachedQuestions?.length === 5) {
      console.log('Retrieved self-assessment questions from cache for:', { role, section });
      return res.json(cachedQuestions);
    }

    // Ensure we have the global learning path data loaded
    if (!globalLearningPathData) {
      console.log('Learning path data not loaded for self-assessment, initializing...');
      await initializeLearningPathData();

      if (!globalLearningPathData) {
        throw new Error('Learning path data could not be loaded');
      }
    }

    console.log('No cached self-assessment questions found, generating new questions');

    // Define the generate_self_assessment_questions function for o3-mini
    const generate_self_assessment_questions = {
      type: "function",
      function: {
        name: "generate_self_assessment_questions",
        description: "Generate role-specific self-assessment questions for skill evaluation",
        parameters: {
          type: "object",
          properties: {
            questions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  question: {
                    type: "string",
                    description: "A role-specific self-assessment question asking about user's skill level with a scenario relevant to their job role"
                  },
                  options: {
                    type: "array",
                    items: { type: "string" },
                    minItems: 3,
                    maxItems: 3,
                    description: "Three options representing increasing levels of skill/proficiency from basic to advanced"
                  },
                  skillArea: {
                    type: "string",
                    description: "The specific skill area being assessed, matching one of the course topics"
                  },
                  type: {
                    type: "string",
                    enum: ["self-assessment"],
                    description: "The type of question (always 'self-assessment')"
                  }
                },
                required: ["question", "options", "skillArea", "type"]
              }
            }
          },
          required: ["questions"]
        }
      }
    };

    // Extract relevant courses and competency keywords for the role and section
    const extractedData = extractRelevantCoursesForRoleAndSection(role, section, framework, globalLearningPathData);

    // Extract courses directly from the framework as intended
    const courses = extractCoursesFromFramework(framework);
    const competencyKeywords = extractedData.competencyKeywords || [];
    const roleKeywords = extractedData.roleKeywords || [];

    // Calculate tokens for API call
    const { encode } = require('gpt-tokenizer');
    const messages = [
      {
        role: "system",
        content: `You are an expert assessment designer specializing in Microsoft 365 and cybersecurity self-assessments. Create questions that help users evaluate their own skill levels.`
      },
      {
        role: "user",
        content: `Create 5 high-quality self-assessment questions for a ${role} at the ${section} level.

IMPORTANT CONTEXT:
- These questions are for a ${role} who needs specific skills from these courses: ${courses.join(', ')}
- The assessment is measuring their current level in the ${section} learning pathway
- Questions should reflect realistic tasks this role would perform using these technologies
- Key competency areas for this role include: ${competencyKeywords.join(', ')}

For each question:
1. Focus on tool proficiency and practical usage scenarios rather than starting with role references
2. Ask about their comfort/skill level with a specific task or tool feature
3. Provide exactly 3 answer options representing increasing competency levels:
   - Option 1: Basic/beginner level skill (minimal capability)
   - Option 2: Intermediate level skill (functional capability)
   - Option 3: Advanced/expert level skill (optimal capability)

PREFERRED FORMATS:
- "How do you handle data visualization in Excel when working with financial reports?"
- "What approach do you take when securing sensitive documents in SharePoint?"
- "When collaborating on a presentation, which PowerPoint features do you utilize?"

FOCUS AREAS:
- Design questions focused on tool proficiency and practical usage scenarios
- Questions should assess HOW the user works with specific tools and features
- Skills specifically needed from these courses: ${courses.join(', ')}
- Tasks typically performed using Microsoft 365 tools
- Productivity challenges they might face

Remember to vary the question formats while maintaining relevance to the ${role} position.`
      }
    ];

    const promptTokens = messages.reduce((acc, msg) =>
      acc + encode(JSON.stringify(msg)).length, 0);

    const contextWindow = 200000; // o3-mini's context size
    const maxResponseTokens = 100000; // o3-mini's max output
    const availableTokens = contextWindow - promptTokens;

    const max_tokens = Math.min(maxResponseTokens, availableTokens);

    if (max_tokens <= 0) {
      throw new Error(`Prompt exceeds context window (${promptTokens}/${contextWindow} tokens)`);
    }

    const completion = await openai.chat.completions.create({
      model: "o3-mini",
      messages: messages,
      tools: [generate_self_assessment_questions],
      tool_choice: { type: "function", function: { name: "generate_self_assessment_questions" } },
      max_completion_tokens: max_tokens,
      reasoning_effort: "low"
    });

    let selfAssessmentQuestions = [];

    try {
      const message = completion.choices[0].message;

      if (message.tool_calls && message.tool_calls.length > 0 &&
          message.tool_calls[0].function.name === "generate_self_assessment_questions") {

        selfAssessmentQuestions = JSON.parse(message.tool_calls[0].function.arguments).questions;

        // Validation and question improvement without forcing role references
        selfAssessmentQuestions = selfAssessmentQuestions.map(q => {
          // Keep the original question without forcing role references
          const question = q.question;

          // Ensure skill area is set appropriately
          const skillArea = q.skillArea || normalizeCourse(q.skillArea || 'General', courses);

          // Ensure options represent increasing skill levels
          let options = q.options.slice(0, 3); // Ensure exactly 3 options

          // Sort options by length as a proxy for complexity - longer answers tend to be more detailed
          if (options.length === 3 && options[0].length > options[2].length) {
            options = [...options].sort((a, b) => a.length - b.length);
          }

          return {
            question: question,
            options: options,
            skillArea: skillArea,
            type: "self-assessment" // Ensure type is set
          };
        });
      }
    } catch (parseError) {
      console.warn('Error parsing self-assessment questions:', parseError);
      return res.status(500).json({
        error: 'Failed to generate self-assessment questions',
        details: parseError.message
      });
    }

    // Trim to exactly 5 questions
    selfAssessmentQuestions = selfAssessmentQuestions.slice(0, 5);

    // Cache the questions if we have a full set with enhanced metadata
    if (selfAssessmentQuestions.length === 5) {
      try {
        const cacheKey = `${role}_${section}_self_assessment`;
        await firestore.collection('questionCache').doc(cacheKey).set({
          questions: selfAssessmentQuestions,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          // Add these fields to improve future generation
          role: role,
          roleKeywords: roleKeywords,
          competencyKeywords: competencyKeywords,
          courses: courses,
          section: section
        });
        console.log('Successfully cached self-assessment questions for:', { role, section });
      } catch (cacheError) {
        console.warn('Failed to cache self-assessment questions:', cacheError);
      }
    }

    // Track this request to prevent duplicates
    if (email) {
      trackRequest(email, 'generate-self-assessment', { role, section });
    }

    res.json(selfAssessmentQuestions);

  } catch (error) {
    console.error('Error generating self-assessment questions:', error);
    // Release the request if it fails
    if (email) {
      releaseRequest(email, 'generate-self-assessment', { role, section });
    }
    res.status(500).json({
      error: 'Failed to generate self-assessment questions',
      details: error.message
    });
  }
});

// Add helper function for caching self-assessment questions
async function getCachedSelfAssessmentQuestions(role, section) {
  try {
    const cacheKey = `${role}_${section}_self_assessment`;
    const cached = await firestore.collection('questionCache').doc(cacheKey).get();

    if (cached.exists) {
      const data = cached.data();
      const cacheAge = Date.now() - data.timestamp.toDate();
      const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      if (cacheAge < CACHE_TTL) {
        console.log(`Using cached self-assessment questions for ${role}/${section}, cache age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
        return data.questions;
      } else {
        console.log(`Cache expired for ${role}/${section} self-assessment, age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
      }
    }
    return null;
  } catch (error) {
    console.warn('Self-assessment cache retrieval failed:', error);
    return null;
  }
}


// Add proper error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('UNCAUGHT EXCEPTION:', error);
  // Log to a monitoring service if available
});

app.listen(port, async () => {
  console.log(`Server is running on http://localhost:${port}`);

  // Initialize learning path data at startup
  await initializeLearningPathData();
});