/**
 * English Proficiency Assessment Module
 * Handles the English assessment flow for student users
 */

class EnglishAssessment {
  constructor() {
    this.timeLimit = 30 * 60; // 30 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    this.minResponseLength = 50; // Minimum characters for submission
  }

  /**
   * Initialize the English assessment
   */
  init() {
    this.setupEventListeners();
    this.startTimer();
    this.updateCharacterCount();
    console.log('English assessment initialized');
  }

  /**
   * Setup event listeners for the assessment
   */
  setupEventListeners() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');
    const returnBtn = document.getElementById('return-to-portal');

    // Character count and submit button state
    textarea.addEventListener('input', () => {
      this.updateCharacterCount();
      this.updateSubmitButtonState();
    });

    // Submit assessment
    submitBtn.addEventListener('click', () => {
      this.submitAssessment();
    });

    // Return to portal button
    if (returnBtn) {
      returnBtn.addEventListener('click', () => {
        window.location.href = 'https://barefootelearning.etraininglibrary.com/';
      });
    }

    // Prevent accidental page refresh
    window.addEventListener('beforeunload', (e) => {
      if (!this.isSubmitted && this.timeRemaining < this.timeLimit) {
        e.preventDefault();
        e.returnValue = 'Your English assessment is in progress. Are you sure you want to leave?';
      }
    });
  }

  /**
   * Start the countdown timer
   */
  startTimer() {
    this.updateTimerDisplay();
    
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;
      this.updateTimerDisplay();
      
      if (this.timeRemaining <= 0) {
        this.timeUp();
      }
    }, 1000);
  }

  /**
   * Update the timer display
   */
  updateTimerDisplay() {
    const timerElement = document.getElementById('english-timer');
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    timerElement.textContent = timeString;
    
    // Add warning classes based on time remaining
    timerElement.classList.remove('warning', 'critical');
    
    if (this.timeRemaining <= 300) { // 5 minutes
      timerElement.classList.add('critical');
    } else if (this.timeRemaining <= 600) { // 10 minutes
      timerElement.classList.add('warning');
    }
  }

  /**
   * Handle time up scenario
   */
  timeUp() {
    clearInterval(this.timerInterval);
    
    const textarea = document.getElementById('english-response');
    const response = textarea.value.trim();
    
    if (response.length >= this.minResponseLength) {
      // Auto-submit if there's sufficient content
      this.submitAssessment();
    } else {
      // Show time up message and force submission
      alert('Time is up! Your response will be submitted as is.');
      this.submitAssessment();
    }
  }

  /**
   * Update character count display
   */
  updateCharacterCount() {
    const textarea = document.getElementById('english-response');
    const charCountElement = document.getElementById('char-count');
    const currentLength = textarea.value.length;
    
    charCountElement.textContent = currentLength;
    
    // Change color based on length
    if (currentLength >= 4500) {
      charCountElement.style.color = '#dc2626'; // Red
    } else if (currentLength >= 4000) {
      charCountElement.style.color = '#ea580c'; // Orange
    } else {
      charCountElement.style.color = '#64748b'; // Default gray
    }
  }

  /**
   * Update submit button state based on response length
   */
  updateSubmitButtonState() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');
    const response = textarea.value.trim();
    
    if (response.length >= this.minResponseLength) {
      submitBtn.disabled = false;
    } else {
      submitBtn.disabled = true;
    }
  }

  /**
   * Submit the English assessment
   */
  async submitAssessment() {
    if (this.isSubmitted) return;
    
    const textarea = document.getElementById('english-response');
    const response = textarea.value.trim();
    
    if (response.length < this.minResponseLength) {
      alert(`Please write at least ${this.minResponseLength} characters before submitting.`);
      return;
    }

    this.isSubmitted = true;
    clearInterval(this.timerInterval);
    
    // Show loading state
    const submitBtn = document.getElementById('submit-english-assessment');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="btn-text">Analyzing...</span>';
    submitBtn.disabled = true;

    try {
      // Get user information
      const email = document.getElementById('email').value.trim();
      const userType = document.querySelector('input[name="user-type"]:checked')?.value;
      const studentLevel = document.getElementById('student-level').value;
      
      // Send to AI for analysis
      const analysisResult = await this.analyzeEnglishProficiency(response, email, studentLevel);
      
      // Store results in database
      await this.storeEnglishAssessmentResults(email, response, analysisResult);
      
      // Determine next step based on score
      if (analysisResult.score >= 16) {
        // L2/GCSE level - proceed to digital skills assessment
        this.proceedToDigitalSkills();
      } else {
        // Below L2 - show completion message
        this.showCompletionMessage(analysisResult);
      }
      
    } catch (error) {
      console.error('Error submitting English assessment:', error);
      alert('An error occurred while processing your assessment. Please try again.');
      
      // Reset button state
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
      this.isSubmitted = false;
    }
  }

  /**
   * Send response to AI for analysis
   */
  async analyzeEnglishProficiency(response, email, studentLevel) {
    const analysisResponse = await fetch('/api/analyze-english-proficiency', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        response: response,
        email: email,
        studentLevel: studentLevel,
        timeSpent: this.timeLimit - this.timeRemaining
      }),
    });

    if (!analysisResponse.ok) {
      throw new Error(`Analysis failed: ${analysisResponse.statusText}`);
    }

    return await analysisResponse.json();
  }

  /**
   * Store English assessment results in database
   */
  async storeEnglishAssessmentResults(email, response, analysisResult) {
    const userType = document.querySelector('input[name="user-type"]:checked')?.value;
    const studentLevel = document.getElementById('student-level').value;
    
    // Update user document with English assessment data
    const companyRef = db.collection('companies').doc(userCompany);
    const userRef = companyRef.collection('users').doc(email);
    
    await userRef.update({
      englishProficiencyScore: analysisResult.score,
      englishProficiencyLevel: analysisResult.level,
      englishAssessmentCompleted: true,
      englishResponse: response,
      englishAssessmentTimestamp: firebase.firestore.FieldValue.serverTimestamp(),
      timeSpentOnEnglish: this.timeLimit - this.timeRemaining,
      updatedAt: firebase.firestore.FieldValue.serverTimestamp()
    });

    console.log('English assessment results stored:', {
      score: analysisResult.score,
      level: analysisResult.level,
      email: email
    });
  }

  /**
   * Proceed to digital skills assessment for L2/GCSE students
   */
  proceedToDigitalSkills() {
    console.log('Student qualified for digital skills assessment');
    
    // Hide English assessment container
    document.getElementById('english-assessment-container').style.display = 'none';
    
    // Show skills gap analyzer container
    document.getElementById('skills-gap-analyzer-container').classList.remove('hidden');
    
    // Initialize framework for the student
    const email = document.getElementById('email').value.trim();
    const userType = document.querySelector('input[name="user-type"]:checked')?.value;
    const studentLevel = document.getElementById('student-level').value;
    const rawRole = `Student - ${studentLevel}`;
    
    // Fetch framework data
    window.fetchFrameworkData(rawRole).catch(error => {
      console.error('Error fetching framework data:', error);
      alert('An error occurred while preparing your assessment. Please try again.');
    });
  }

  /**
   * Show completion message for students below L2 level
   */
  showCompletionMessage(analysisResult) {
    console.log('Student did not qualify for digital skills assessment:', analysisResult);
    
    // Hide English assessment container
    document.getElementById('english-assessment-container').style.display = 'none';
    
    // Show completion container
    document.getElementById('english-completion-container').style.display = 'flex';
  }

  /**
   * Clean up timer and event listeners
   */
  destroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }
}

// Global instance
window.englishAssessment = null;

/**
 * Initialize English assessment for student users
 */
function initializeEnglishAssessment() {
  if (window.englishAssessment) {
    window.englishAssessment.destroy();
  }
  
  window.englishAssessment = new EnglishAssessment();
  window.englishAssessment.init();
}

/**
 * Check if user should take English assessment
 */
function shouldTakeEnglishAssessment() {
  const userType = document.querySelector('input[name="user-type"]:checked')?.value;
  return userType === 'student';
}
